import { Client } from 'pg';

export class PgDbManager {
  static dbName = process.env.PG_DB;

  constructor() {}

  static createClient() {
    return new Client({
      host: process.env.PG_HOST,
      port: parseInt(process.env.PG_PORT || '5432'),
      user: process.env.PG_USER,
      password: process.env.PG_PASSWORD,
    });
  }

  static async createDb() {
    const client = this.createClient();

    await client.connect();

    const res = await client.query('SELECT 1 FROM pg_database WHERE datname = $1', [this.dbName]);
    if (res.rowCount === 0) {
      await client.query(`CREATE DATABASE ${this.dbName}`);
      console.log(`Created database ${this.dbName}`);
    } else {
      console.log(`Database ${this.dbName} already exists`);
    }

    await client.end();
  }

  static async deleteDb() {
    const client = this.createClient();

    await client.connect();

    // Close all connections to the database
    await client.query(
      `
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = $1 AND pid <> pg_backend_pid();
      `,
      [this.dbName],
    );

    await client.query(`DROP DATABASE ${this.dbName}`);
    console.log(`Deleted database ${this.dbName}`);

    await client.end();
  }
}
