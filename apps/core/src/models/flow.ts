import { z } from 'zod';
import { nullableStringIdSchema } from '@libs/common/utils';

export const FlowSchema = z.object({
  id: z.coerce.string().describe('Flow id'),

  name: z.string().min(2, 'Name must be at least 2 characters').describe('Flow name'),

  description: z.string().nullable().describe('Flow description'),

  prevId: nullableStringIdSchema().describe('Flow previous id'),
});

export type FlowModel = z.infer<typeof FlowSchema>;
