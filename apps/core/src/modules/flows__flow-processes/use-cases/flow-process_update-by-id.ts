import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcess } from '@core/database';
import { NumericId } from '@libs/common/database';
import { PartialUpdateFlowProcessDto, UpdateFlowProcessDto } from '../flow-processes.dto';
import { FlowProcessesService } from '../flow-processes.service';

@Injectable()
export class UpdateFlowProcessUseCase implements UseCase {
  private readonly logger = new Logger(UpdateFlowProcessUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly processesService: FlowProcessesService,
  ) {}

  async execute({
    processId,
    dto,
  }: {
    processId: string;
    dto: UpdateFlowProcessDto | PartialUpdateFlowProcessDto;
  }): Promise<FlowProcess> {
    const { name, description } = dto;
    this.logger.verbose({ msg: 'Started updating process', data: { processId, dto } });

    this.logger.verbose({ msg: 'Finding process to update', data: { processId } });

    const process = await this.db.flowProcesses.findOne({
      where: { id: NumericId(processId) },
    });

    if (!process) throw new NotFoundException('Process not found', 'PROCESS_NOT_FOUND');

    this.logger.verbose({ msg: 'process found', data: process });

    this.logger.verbose({ msg: 'Updating process', processId });

    const modifiedProcess = this.processesService.normalizeEntityValues(process, {
      name,
      description,
    });

    await this.db.flowProcesses.save(modifiedProcess);

    this.logger.log({
      msg: 'Process updated',
      data: {
        incomingData: { processId, dto },
        modifiedProcess,
      },
    });

    return modifiedProcess;
  }
}
