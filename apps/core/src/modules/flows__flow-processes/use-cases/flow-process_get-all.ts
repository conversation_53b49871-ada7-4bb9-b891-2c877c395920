import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService, FlowProcess } from '@core/database';
import { NumericId } from '@libs/common/database';

@Injectable()
export class GetAllFlowProcessesUseCase implements UseCase {
  private readonly logger = new Logger(GetAllFlowProcessesUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute({ flowId }: { flowId: string }): Promise<FlowProcess[]> {
    this.logger.verbose({ msg: 'Started getting all processes', data: { flowId } });

    const processes = await this.db.flowProcesses.find({
      where: {
        flow: { id: NumericId(flowId) },
      },
    });

    this.logger.verbose({
      msg: 'All processes found',
      data: {
        incomingData: { flowId },
        processesFound: processes,
      },
    });

    return processes;
  }
}
