import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcess } from '@core/database';
import { NumericId } from '@libs/common/database';

@Injectable()
export class GetFlowProcessByIdUseCase implements UseCase {
  private readonly logger = new Logger(GetFlowProcessByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute({ processId }: { processId: string }): Promise<FlowProcess> {
    this.logger.verbose({ msg: 'Started getting process', data: { processId } });

    const process = await this.db.flowProcesses.findOne({
      where: { id: NumericId(processId) },
    });

    if (!process) throw new NotFoundException('Process not found', 'PROCESS_NOT_FOUND');

    this.logger.verbose({
      msg: 'Process found',
      data: {
        incomingData: { processId },
        foundProcess: process,
      },
    });

    return process;
  }
}
