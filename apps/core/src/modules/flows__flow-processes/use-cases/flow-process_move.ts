import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IsNull, Like } from 'typeorm';
import { DatabaseService, FlowProcess } from '@core/database';
import { NumericId } from '@libs/common/database';
import { MoveFlowProcessDto } from '../flow-processes.dto';
import { FlowProcessesService } from '../flow-processes.service';

@Injectable()
export class MoveFlowProcessUseCase implements UseCase {
  private readonly logger = new Logger(MoveFlowProcessUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly processesService: FlowProcessesService,
  ) {}

  async execute({
    processId,
    flowId,
    dto,
  }: {
    processId: string;
    flowId: string;
    dto: MoveFlowProcessDto;
  }): Promise<FlowProcess> {
    /** This is a data with a new destination of the moving flow process */
    const { parentId: targetParentId, prevId: targetPrevId } = dto;

    this.logger.verbose({ msg: 'Started moving process', data: { processId, flowId, dto } });

    // TODO: optimize

    const [movingProcess, nextProcess, targetNextProcess, targetParentProcess] =
      await this.db.flowProcesses.manager.transaction(async entityManager => {
        this.logger.verbose({ msg: 'Finding data' });

        // TODO: find many and then use find by result array

        const [movingProcess, nextProcess, targetNextProcess, targetParentProcess] = await Promise.all([
          /** Moving process */
          entityManager.findOne(FlowProcess, {
            where: { id: NumericId(processId) },
          }),

          /** Next process (it is the process with prevId = movingProcess.id) */
          entityManager.findOne(FlowProcess, {
            where: { flow: { id: NumericId(flowId) }, prevId: NumericId(processId) },
          }),

          /** Target next process (it is the process with prevId = targetPrevId) */
          /** We don't need the target process itself, we just need to update next process's prevId */
          entityManager.findOne(FlowProcess, {
            where: {
              flow: { id: NumericId(flowId) },
              parentId: targetParentId ? NumericId(targetParentId) : IsNull(),
              prevId: targetPrevId ? NumericId(targetPrevId) : IsNull(),
            },
          }),
          /** Target parent process */
          targetParentId
            ? entityManager.findOne(FlowProcess, {
                where: { id: NumericId(targetParentId) },
              })
            : null,
        ]);

        this.logger.verbose({
          msg: 'Data found',
          data: {
            process: movingProcess,
            nextProcess: nextProcess,
            targetNextProcess: targetNextProcess,
            targetParentProcess: targetParentProcess,
          },
        });

        if (!movingProcess) throw new NotFoundException('Process not found', 'PROCESS_NOT_FOUND');

        /** Shift nextProcess above */
        if (nextProcess) {
          this.logger.verbose({ msg: 'Next process found', data: { processId: nextProcess.id } });

          this.logger.verbose({
            msg: 'Shifting next process above',
            data: { processId: nextProcess.id, prevId: movingProcess.prevId },
          });

          nextProcess.prevId = movingProcess.prevId;
          await entityManager.save(FlowProcess, nextProcess);
        } else {
          this.logger.verbose({ msg: 'No next process found' });
        }

        /** Move process */
        const newProcessMPath = await this.processesService.generateUniqueMaterializedPath(entityManager, {
          flowId,
          parentMPath: targetParentProcess ? targetParentProcess.mPath : undefined,
        });

        this.logger.verbose({
          msg: 'Moving process to new parent and prev process',
          data: {
            processId: movingProcess.id,
            processMPath: newProcessMPath,
            targetParentId,
            targetPrevId,
          },
        });

        const currentProcessMPath = movingProcess.mPath;

        movingProcess.parentId = NumericId(targetParentId);
        movingProcess.prevId = NumericId(targetPrevId);
        movingProcess.mPath = newProcessMPath;

        await entityManager.save(FlowProcess, movingProcess);

        this.logger.verbose({ msg: 'Process moved', data: { processId: movingProcess.id } });

        /** Update mPath for children */
        this.logger.verbose({
          msg: 'Updating mPath for children',
          data: { currentProcessMPath, newProcessMPath },
        });

        await entityManager.update(
          FlowProcess,
          {
            flow: { id: NumericId(flowId) },
            mPath: Like(`${currentProcessMPath}/%`),
          },
          {
            mPath: newProcessMPath,
          },
        );

        /** Shift targetNextProcess below */
        if (targetNextProcess) {
          this.logger.verbose({
            msg: 'Target next process found',
            data: { processId: targetNextProcess.id },
          });

          this.logger.verbose({
            msg: 'Shifting target process below',
            data: { processId: targetNextProcess.id, prevId: movingProcess.id },
          });

          targetNextProcess.prevId = movingProcess.id;
          await entityManager.save(FlowProcess, targetNextProcess);
        } else {
          this.logger.verbose({ msg: 'No target next process found' });
        }

        return [movingProcess, nextProcess, targetNextProcess, targetParentProcess];
      });

    this.logger.log({
      msg: 'Process moved',
      data: {
        incomingData: { processId, flowId, dto },
        modifiedProcess: movingProcess,
        modifiedData: {
          nextProcess,
          targetNextProcess,
          targetParentProcess,
        },
      },
    });

    return movingProcess;
  }
}
