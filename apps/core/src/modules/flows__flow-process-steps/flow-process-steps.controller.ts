import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiOperation, ApiParam } from '@nestjs/swagger';
import { z } from 'zod';
import { FlowProcessStepSchema } from '@core/models';
import {
  ApiError,
  ApiSuccessfulResponse,
  EMPTY_RESPONSE,
  HttpResponse,
  SanitizeResponseWithZod,
} from '@libs/common/api';
import {
  CreateFlowProcessStepDto,
  FlowProcessStepDto,
  MoveFlowProcessStepDto,
  PartialUpdateFlowProcessStepDto,
  UpdateFlowProcessStepDto,
} from './flow-process-steps.dto';
import {
  CreateFlowProcessStepUseCase,
  DeleteFlowProcessStepByIdUseCase,
  GetAllFlowProcessStepsUseCase,
  GetFlowProcessStepByIdUseCase,
  MoveFlowProcessStepUseCase,
  UpdateFlowProcessStepUseCase,
} from './use-cases';

const ApiParamFlowProcessStepId = () =>
  ApiParam({ name: 'stepId', required: true, description: 'Step identifier', type: 'string' });

@Controller('flows/:flowId/processes/:processId/steps')
@ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' })
@ApiParam({ name: 'processId', required: true, description: 'Process identifier', type: 'string' })
export class FlowProcessStepsController {
  constructor(
    private readonly createFlowProcessStepUseCase: CreateFlowProcessStepUseCase,
    private readonly updateFlowProcessStepUseCase: UpdateFlowProcessStepUseCase,
    private readonly deleteFlowProcessStepByIdUseCase: DeleteFlowProcessStepByIdUseCase,
    private readonly moveFlowProcessStepUseCase: MoveFlowProcessStepUseCase,
    private readonly getFlowProcessStepByIdUseCase: GetFlowProcessStepByIdUseCase,
    private readonly getAllFlowProcessStepsUseCase: GetAllFlowProcessStepsUseCase,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create step' })
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Step created', FlowProcessStepDto)
  @ApiError(HttpStatus.CONFLICT, 'Parent step not found', 'PARENT_STEP_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async createStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Body() dto: CreateFlowProcessStepDto,
  ) {
    const step = await this.createFlowProcessStepUseCase.execute({ processId, dto });
    return new HttpResponse({
      statusCode: HttpStatus.CREATED,
      data: step,
      message: 'Step created',
    });
  }

  @Put(':stepId')
  @ApiOperation({ summary: 'Update step' })
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Step updated', FlowProcessStepDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Step not found', 'STEP_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async updateStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
    @Body() dto: UpdateFlowProcessStepDto,
  ) {
    const step = await this.updateFlowProcessStepUseCase.execute({ stepId, dto });
    return new HttpResponse({ data: step, message: 'Step updated' });
  }

  @Patch(':stepId')
  @ApiOperation({ summary: 'Partial update step' })
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Step updated', FlowProcessStepDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Step not found', 'STEP_NOT_FOUND')
  @SanitizeResponseWithZod(EMPTY_RESPONSE)
  async partialUpdateStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
    @Body() dto: PartialUpdateFlowProcessStepDto,
  ) {
    await this.updateFlowProcessStepUseCase.execute({ stepId, dto });
    return new HttpResponse({ message: 'Step updated' });
  }

  @Delete(':stepId')
  @ApiOperation({ summary: 'Delete step' })
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Step deleted', FlowProcessStepDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Step not found', 'STEP_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async deleteStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
  ) {
    const step = await this.deleteFlowProcessStepByIdUseCase.execute({ processId, stepId });
    return new HttpResponse({ data: step, message: 'Step deleted' });
  }

  @Post(':stepId/move')
  @ApiOperation({ summary: 'Move step' })
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Step moved', FlowProcessStepDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Step not found', 'STEP_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async moveStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
    @Body() dto: MoveFlowProcessStepDto,
  ) {
    const step = await this.moveFlowProcessStepUseCase.execute({ stepId, processId, dto });
    return new HttpResponse({ data: step, message: 'Step moved' });
  }

  @Get(':stepId')
  @ApiOperation({ summary: 'Get step' })
  @ApiParamFlowProcessStepId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Step found', FlowProcessStepDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Step not found', 'STEP_NOT_FOUND')
  @SanitizeResponseWithZod(FlowProcessStepSchema)
  async getStep(
    @Param('flowId') flowId: string,
    @Param('processId') processId: string,
    @Param('stepId') stepId: string,
  ) {
    const step = await this.getFlowProcessStepByIdUseCase.execute({ stepId });
    return new HttpResponse({ data: step, message: 'Step found' });
  }

  @Get()
  @ApiOperation({ summary: 'Get all process steps' })
  @ApiSuccessfulResponse(HttpStatus.OK, 'Process steps found', [FlowProcessStepDto])
  @SanitizeResponseWithZod(z.array(FlowProcessStepSchema))
  async getSteps(@Param('flowId') flowId: string, @Param('processId') processId: string) {
    const steps = await this.getAllFlowProcessStepsUseCase.execute({ processId: processId });
    return new HttpResponse({ data: steps, message: 'Process steps found' });
  }
}
