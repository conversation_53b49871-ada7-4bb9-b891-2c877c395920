import { Test, TestingModule } from '@nestjs/testing';
import { Flow } from '@core/database';
import { FlowsService } from './flows.service';

describe('FlowsService', () => {
  let service: FlowsService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FlowsService],
    }).compile();

    service = module.get(FlowsService);
  });

  describe('normalizeEntityValues', () => {
    it('sets description to null if it is an empty string', () => {
      const flow = new Flow();
      flow.description = '';
      const normalizedFlow = service.normalizeEntityValues(flow, {});
      expect(normalizedFlow.description).toBeNull();
    });

    it('removes undefined values', () => {
      const newDescription = 'new description';
      const flow = new Flow();
      flow.description = newDescription;
      const normalizedFlow = service.normalizeEntityValues(flow, { description: undefined });
      expect(normalizedFlow.description).toBe(newDescription);
    });
  });
});
