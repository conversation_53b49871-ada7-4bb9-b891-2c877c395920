{"name": "easy-flow--backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/core/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "ts-node ./tests/run-e2e.ts"}, "dependencies": {"@elastic/ecs-pino-format": "^1.5.0", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "ioredis": "^5.7.0", "nanoid": "^3.3.11", "nest-commander": "^3.17.0", "nestjs-pino": "^4.4.0", "nestjs-zod": "^4.3.1", "passport-jwt": "^4.0.1", "pg": "^8.16.2", "pino": "^9.7.0", "pino-socket": "^7.4.0", "redlock": "^5.0.0-beta.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "typeorm": "^0.3.25", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/minimist": "^1.2.5", "@types/node": "^22.10.7", "@types/pg": "^8.15.5", "@types/supertest": "^6.0.3", "cross-env": "^10.0.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "globals": "^16.2.0", "jest": "^30.0.3", "minimist": "^1.2.8", "pino-pretty": "^13.0.0", "prettier": "^3.6.0", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0"}}