import { Config } from 'jest';

const baseConfig: Config = {
  testEnvironment: 'node',
  moduleFileExtensions: ['js', 'json', 'ts'],
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  moduleNameMapper: {
    '^@tests(|/.*)$': '<rootDir>/tests/$1',
    '^@libs/common(|/.*)$': '<rootDir>/libs/common/src/$1',
    '^@libs/redis$': '<rootDir>/libs/redis/src',
    '^@core(|/.*)$': '<rootDir>/apps/core/src/$1',
  },
};

let config: Config;

const e2eApp = process.env.E2E_APP;
if (e2eApp) {
  // E2E tests for specific app
  config = {
    ...baseConfig,
    rootDir: '.',
    testMatch: [`<rootDir>/apps/${e2eApp}/test/**/*.e2e-spec.ts`],
  };
} else {
  // Unit tests for all apps and libs
  config = {
    ...baseConfig,
    verbose: true,
    testRegex: '.*\\.spec\\.ts$',
    collectCoverageFrom: ['**/*.(t|j)s'],
    coverageDirectory: './coverage',
    rootDir: '.',
    roots: ['<rootDir>/apps/', '<rootDir>/libs/'],
  };
}

export default config;
