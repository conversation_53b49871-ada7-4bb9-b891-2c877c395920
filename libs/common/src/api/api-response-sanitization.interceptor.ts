import { Injectable, NestInterceptor, Execution<PERSON>ontext, CallHandler, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, map } from 'rxjs';
import { ZodSchema } from 'zod';
import { isDevelopment } from '../envs';

// https://artosalminen.github.io/posts/how-to-manipulate-nestjs-response

export const EMPTY_RESPONSE = Symbol('EMPTY_RESPONSE');
export const SanitizeResponseWithZod = Reflector.createDecorator<ZodSchema | typeof EMPTY_RESPONSE>();

// TODO: arrays

@Injectable()
export class ZodResponseInterceptor<T extends object> implements NestInterceptor<T> {
  private readonly logger = new Logger(ZodResponseInterceptor.name);

  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<T> {
    const schema = this.reflector.get(SanitizeResponseWithZod, context.getHandler());
    return next.handle().pipe(
      map((data: T) => {
        if (schema === undefined) return data;
        const isHttpResponse = 'data' in data;
        const payload = isHttpResponse ? data.data : data;
        try {
          const parsedData = schema === EMPTY_RESPONSE ? null : (schema.parse(payload) as unknown);
          return (isHttpResponse ? { ...data, data: parsedData } : parsedData) as T;
        } catch (error: unknown) {
          this.logger.error({
            msg: 'ZodResponseInterceptor: Error parsing response',
            error,
            data,
          });
          if (isDevelopment()) throw error; // 500 INTERNAL_ERROR in development
          return data;
        }
      }),
    );
  }
}
