import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { Response } from 'express';
import { ZodValidationException } from 'nestjs-zod';
import { formatHttpException } from './api.resources';

/**
 * Catch all exceptions and convert them to HttpException.
 * Useful to catch RpcException
 */
@Catch()
export class GlobalApiExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalApiExceptionFilter.name);

  catch(error: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    if (error instanceof ZodValidationException) {
      /** check if it is a ZodValidationException */

      const payload = formatHttpException(error);
      payload.details = error.getZodError().issues;
      payload.error = 'VALIDATION_ERROR';

      return response.status(error.getStatus()).json(payload);
    } else if (error instanceof HttpException) {
      /** check if it is a HttpException */

      const errorResponse = error.getResponse();
      const cause =
        typeof errorResponse === 'object' ? (errorResponse as { error: string }).error : undefined;

      const payload = formatHttpException(error, cause);

      return response.status(error.getStatus()).json(payload);
    } else {
      /** convert to InternalServerErrorException */

      const exception = new InternalServerErrorException('INTERNAL_ERROR');

      const payload = formatHttpException(exception);
      payload.timestamp = new Date().toISOString();

      this.logger.error({
        msg: (error as Error).message ?? exception.message,
        // error: (error as Error) ?? exception,
        stack: (error as Error).stack ?? exception.stack,
      });

      response.status(exception.getStatus()).json(payload);
    }
  }
}
