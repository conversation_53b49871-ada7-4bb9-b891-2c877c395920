import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Redis } from 'ioredis';
import Redlock, { Lock, ResourceLockedError } from 'redlock';
import { REDIS_CLIENT } from './redis.module';

@Injectable()
export class RedisLockService implements OnModuleInit {
  private readonly logger = new Logger(RedisLockService.name);

  private redlock: Redlock;

  constructor(@Inject(REDIS_CLIENT) private readonly redis: Redis) {}

  onModuleInit() {
    this.redlock = new Redlock([this.redis], {
      retryCount: 3,
      retryDelay: 200, // ms
      retryJitter: 100, // ms
    });

    this.redlock.on('error', (error: unknown) => {
      if (error instanceof ResourceLockedError) return;
      this.logger.error({ msg: 'A Redis client error occurred', data: { error } });
    });
  }

  async acquireLock(resource: string, ttl: number = 5000): Promise<Lock> {
    try {
      const lock = await this.redlock.acquire([resource], ttl);
      return lock;
    } catch (error: unknown) {
      this.logger.error({ msg: 'Failed to acquire lock', data: { error } });
      // TODO:
      throw new Error(`Failed to acquire lock for ${resource}: ${(error as Error).message}`);
    }
  }

  async releaseLock(lock: Lock): Promise<void> {
    try {
      await lock.release();
    } catch (error: unknown) {
      this.logger.warn({ msg: 'Failed to release lock', data: { error } });
    }
  }
}
