import { DynamicModule, Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { RedisLockService } from './redis-lock.service';

export const REDIS_CLIENT = Symbol('REDIS_CLIENT');

@Global()
@Module({
  // providers: [RedisLockService],
  // exports: [RedisLockService],
})
export class RedisModule {
  static forRootAsync(): DynamicModule {
    return {
      module: RedisModule,
      providers: [
        {
          provide: REDIS_CLIENT,
          inject: [ConfigService],
          useFactory: async (config: ConfigService): Promise<Redis> => {
            const client: Redis = new Redis({
              host: config.get('REDIS_HOST'),
              port: config.get('REDIS_PORT'),
            });

            await new Promise<void>((resolve, reject) => {
              client.once('ready', resolve);
              client.once('error', reject);
            });

            return client;
          },
        },
        RedisLockService,
      ],
      exports: [REDIS_CLIENT],
    };
  }
}
